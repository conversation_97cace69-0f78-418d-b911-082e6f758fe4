/**
 * KaibanJS Tasks for Super Agent Workflow
 *
 * This file defines all the tasks that agents will execute in sequence
 * to complete the super agent workflow using KaibanJS framework.
 * 
 * Following the 7-phase super agent workflow pattern with enhanced
 * task definitions and proper agent assignments.
 */

import { Task } from 'kaibanjs';
import { superAgentTeam } from './agents';

// Console logging utility for Kaiban Tasks
const logKaibanTask = (message: string, data?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`📋 [KAIBAN-TASK] ${timestamp}: ${message}`);
  if (data) {
    console.log(`📝 [KAIBAN-TASK-DATA]:`, data);
  }
};

logKaibanTask('Initializing KaibanJS Super Agent Tasks');

/**
 * Phase 1: Topic Analysis Task (Llama - High Reasoning)
 * Comprehensive topic analysis and research strategy development
 */
export const topicAnalysisTask = new Task({
  description: `Analyze the given topic comprehensively and create a detailed research strategy.
    
    **Your Role:** You are a Senior Research Strategist with expertise in topic analysis and research planning.
    
    **Task Requirements:**
    1. **Topic Breakdown**: Analyze the main topic and identify 4-6 key subtopics
    2. **Key Terms Extraction**: Extract 6-8 important terms including technical terminology
    3. **Search Strategy**: Create 3-4 strategic search queries for comprehensive coverage
    4. **Complexity Assessment**: Evaluate topic complexity (simple/moderate/complex)
    5. **Source Estimation**: Estimate realistic source count needed (8-15 sources)
    6. **Audience Analysis**: Determine target audience intent and expertise level
    7. **Content Angle**: Identify unique perspective or approach for the topic
    8. **Competitive Keywords**: Find terms competitors are ranking for
    9. **Trending Topics**: Identify related topics that are currently popular
    10. **Content Gaps**: Spot missing information in existing content
    11. **User Questions**: Generate questions the audience would ask
    12. **Semantic Keywords**: Extract related terms and concepts
    
    **Output Format:**
    Provide a structured analysis with clear sections for each requirement.
    Focus on creating a comprehensive foundation for the research phases.
    
    **Topic to Analyze:** {topic}
    **Content Type:** {contentType}
    **Target Audience:** {targetAudience}
    **Word Count Target:** {wordCount}`,
  expectedOutput: `Comprehensive topic analysis including:
    - Main topic and subtopics breakdown
    - Key terms and semantic keywords
    - Strategic search queries
    - Complexity assessment and source estimation
    - Audience intent and content angle analysis
    - Competitive keywords and trending topics
    - Content gaps and user questions
    - Research strategy recommendations`,
  agent: superAgentTeam[0], // TopicAnalyst (Qwen)
});

/**
 * Phase 2: Content Strategy Task (Gemini)
 * Develop comprehensive content strategy based on topic analysis
 */
export const contentStrategyTask = new Task({
  description: `Develop a comprehensive content strategy based on the topic analysis.
    
    **Your Role:** You are a Professional Content Strategist specializing in audience-focused content planning.
    
    **Task Requirements:**
    1. **Content Goal Definition**: Define clear content objectives
    2. **Audience Targeting**: Refine target audience based on analysis
    3. **Content Structure**: Design optimal content structure and flow
    4. **Key Messages**: Identify 3-5 core messages to communicate
    5. **Hook Development**: Create compelling opening hooks
    6. **Call-to-Action Strategy**: Design effective CTAs
    7. **SEO Strategy**: Develop keyword strategy and search intent alignment
    8. **Narrative Structure**: Plan story arc and content flow
    9. **Engagement Elements**: Identify opportunities for audience engagement
    10. **Content Differentiation**: Define unique value proposition
    
    **Input:** Use the topic analysis results from the previous task.
    
    **Output Format:**
    Provide a detailed content strategy document with actionable recommendations.`,
  expectedOutput: `Detailed content strategy including:
    - Content goals and audience targeting
    - Recommended content structure and flow
    - Key messages and narrative structure
    - Hook strategies and call-to-action plans
    - SEO strategy with keyword recommendations
    - Engagement and differentiation strategies`,
  agent: superAgentTeam[1], // ContentStrategist (Gemini)
  context: [topicAnalysisTask],
});

/**
 * Phase 3: Primary Research Task (Gemini + Tavily)
 * Conduct comprehensive primary research using Tavily search
 */
export const primaryResearchTask = new Task({
  description: `Conduct comprehensive primary research using the Tavily search tool.
    
    **Your Role:** You are a Senior Research Analyst specializing in information gathering and source evaluation.
    
    **Task Requirements:**
    1. **Execute Search Queries**: Use the strategic queries from topic analysis
    2. **Source Evaluation**: Assess source credibility and authority
    3. **Information Extraction**: Extract key information and insights
    4. **Data Organization**: Structure findings by subtopic and relevance
    5. **Source Documentation**: Maintain detailed source citations
    6. **Quality Filtering**: Filter out low-quality or unreliable sources
    7. **Trend Identification**: Identify current trends and developments
    8. **Expert Insights**: Find expert opinions and authoritative perspectives
    9. **Statistical Data**: Gather relevant statistics and data points
    10. **Recent Developments**: Focus on current and recent information (2024-2025)
    
    **Search Strategy:**
    - Use exact topic searches first
    - Execute targeted queries for specific aspects
    - Search for authoritative and expert sources
    - Focus on recent content and current trends
    - Maximum 10 results per search query
    
    **Tool Usage:**
    Use the Tavily search tool by calling it with your search queries.
    Example: Search for "artificial intelligence trends 2025"
    
    **Input:** Use search queries and strategy from topic analysis and content strategy tasks.`,
  expectedOutput: `Comprehensive research findings including:
    - Organized information by subtopic
    - Source citations and credibility assessments
    - Key insights and expert perspectives
    - Current trends and recent developments
    - Statistical data and supporting evidence
    - Identified information gaps for deep research`,
  agent: superAgentTeam[2], // PrimaryResearcher (Gemini + Tavily)
  context: [topicAnalysisTask, contentStrategyTask],
});

logKaibanTask('Primary research task created with Tavily integration');

/**
 * Phase 4: Gap Analysis Task (Llama - High Reasoning)
 * Analyze research gaps and determine additional research needs
 */
export const gapAnalysisTask = new Task({
  description: `Analyze the primary research results to identify gaps and determine additional research needs.
    
    **Your Role:** You are a Research Quality Analyst with expertise in identifying information gaps and ensuring comprehensive coverage.
    
    **Task Requirements:**
    1. **Coverage Assessment**: Evaluate completeness of primary research
    2. **Gap Identification**: Identify missing information and perspectives
    3. **Priority Analysis**: Rank gaps by importance and impact
    4. **Research Recommendations**: Suggest specific additional research queries
    5. **Source Diversity**: Assess need for different types of sources
    6. **Depth Analysis**: Determine areas needing deeper investigation
    7. **Quality Evaluation**: Assess overall research quality and reliability
    8. **Bias Detection**: Identify potential biases in current research
    9. **Expertise Gaps**: Find areas lacking expert perspectives
    10. **Currency Assessment**: Evaluate need for more recent information
    
    **Analysis Framework:**
    - Compare research against content strategy requirements
    - Identify subtopics with insufficient coverage
    - Assess source diversity and authority
    - Evaluate information recency and relevance
    - Determine critical missing elements
    
    **Input:** Use results from primary research and compare against topic analysis and content strategy.`,
  expectedOutput: `Comprehensive gap analysis including:
    - Identified information gaps with priority rankings
    - Specific additional research recommendations
    - Source diversity and quality assessment
    - Areas requiring deeper investigation
    - Targeted search queries for deep research phase`,
  agent: superAgentTeam[3], // GapAnalyst (Qwen)
  context: [topicAnalysisTask, contentStrategyTask, primaryResearchTask],
});

logKaibanTask('Gap analysis task created for research quality assessment');

/**
 * Phase 5: Deep Research Task (Gemini + Tavily)
 * Conduct targeted deep research based on gap analysis
 */
export const deepResearchTask = new Task({
  description: `Conduct targeted deep research to fill identified gaps and enhance content comprehensiveness.
    
    **Your Role:** You are a Specialized Research Expert who excels at finding specific, detailed information to fill knowledge gaps.
    
    **Task Requirements:**
    1. **Targeted Searches**: Execute specific queries to fill identified gaps
    2. **Expert Source Finding**: Locate authoritative expert perspectives
    3. **Detailed Investigation**: Dive deep into complex aspects
    4. **Specialized Information**: Find technical or specialized details
    5. **Case Studies**: Locate relevant examples and case studies
    6. **Recent Developments**: Find latest news and developments
    7. **Statistical Enhancement**: Gather additional data and statistics
    8. **Comparative Analysis**: Find comparative information and benchmarks
    9. **Implementation Details**: Research practical applications and methods
    10. **Validation**: Cross-reference and validate critical information
    
    **Search Strategy:**
    - Focus on gap analysis recommendations
    - Use specialized and technical search terms
    - Target expert and authoritative sources
    - Search for recent developments and updates
    - Look for case studies and practical examples
    
    **Tool Usage:**
    Use the Tavily search tool with targeted queries based on gap analysis.
    
    **Input:** Use gap analysis recommendations and specific research needs identified.`,
  expectedOutput: `Targeted deep research results including:
    - Specific information filling identified gaps
    - Expert perspectives and authoritative sources
    - Detailed technical or specialized information
    - Case studies and practical examples
    - Recent developments and updates
    - Enhanced statistical data and evidence`,
  agent: superAgentTeam[4], // DeepResearcher (Gemini + Tavily)
  context: [topicAnalysisTask, contentStrategyTask, primaryResearchTask, gapAnalysisTask],
});

logKaibanTask('Deep research task created for targeted investigation');

/**
 * Phase 6: Content Generation Task (Gemini)
 * Generate high-quality content based on comprehensive research
 */
export const contentGenerationTask = new Task({
  description: `Create engaging, well-structured, and authoritative content based on comprehensive research.

    **Your Role:** You are an Expert Content Creator with exceptional writing skills and deep understanding of audience engagement.

    **Task Requirements:**
    1. **Content Structure**: Follow the content strategy structure and flow
    2. **Research Integration**: Seamlessly integrate research findings
    3. **Engaging Writing**: Create compelling and readable content
    4. **Authority Building**: Establish credibility through expert sources
    5. **Audience Focus**: Write for the specified target audience
    6. **Hook Implementation**: Use effective opening hooks from strategy
    7. **Flow Optimization**: Ensure smooth transitions between sections
    8. **Evidence Integration**: Include supporting data and statistics
    9. **Source Attribution**: Properly cite sources and references
    10. **Call-to-Action**: Include strategic CTAs as planned
    11. **SEO Integration**: Naturally incorporate target keywords
    12. **Readability**: Maintain appropriate reading level and clarity

    **Content Guidelines:**
    - Follow the target word count: {wordCount}
    - Use the specified tone: {tone}
    - Address the target audience: {targetAudience}
    - Include 2025 trends and current year information
    - Reference authoritative sources from research
    - Create paragraph-sized data points for better context
    - Strictly adhere to the exact topic without deviation

    **Input:** Use all previous task results including research findings, content strategy, and gap analysis.`,
  expectedOutput: `High-quality content including:
    - Complete article following content strategy
    - Proper integration of research findings
    - Engaging hooks and smooth flow
    - Authoritative source citations
    - Strategic call-to-actions
    - SEO-optimized content with target keywords
    - Content meeting specified word count and tone`,
  agent: superAgentTeam[5], // ContentWriter (Gemini)
  context: [topicAnalysisTask, contentStrategyTask, primaryResearchTask, gapAnalysisTask, deepResearchTask],
});

/**
 * Phase 7: Quality Assurance Task (Llama - High Reasoning)
 * Review and optimize the final content
 */
export const qualityAssuranceTask = new Task({
  description: `Ensure content meets highest quality standards through comprehensive review and optimization.

    **Your Role:** You are a Senior Content Quality Analyst with expertise in fact-checking, readability optimization, and content enhancement.

    **Task Requirements:**
    1. **Fact Verification**: Verify all claims and statistics
    2. **Source Validation**: Ensure all sources are properly cited and credible
    3. **Readability Analysis**: Assess and optimize reading level
    4. **Structure Review**: Evaluate content organization and flow
    5. **Engagement Assessment**: Review hooks, transitions, and CTAs
    6. **SEO Optimization**: Verify keyword integration and optimization
    7. **Accuracy Check**: Ensure technical accuracy and currency
    8. **Bias Detection**: Identify and address potential biases
    9. **Completeness Review**: Ensure all key points are covered
    10. **Final Polish**: Enhance clarity, coherence, and impact
    11. **Quality Scoring**: Provide comprehensive quality metrics
    12. **Improvement Recommendations**: Suggest specific enhancements

    **Quality Standards:**
    - Factual accuracy and source reliability
    - Appropriate readability for target audience
    - Strong engagement and storytelling elements
    - Effective SEO optimization
    - Comprehensive topic coverage
    - Professional writing quality
    - Clear structure and flow

    **Input:** Use the generated content and all supporting research and strategy materials.`,
  expectedOutput: `Comprehensive quality assessment including:
    - Fact-checking results and source validation
    - Readability analysis and optimization recommendations
    - Content quality scores and metrics
    - SEO optimization assessment
    - Engagement and structure evaluation
    - Final polished content with improvements
    - Quality assurance report with recommendations`,
  agent: superAgentTeam[6], // QualityAssurance (Qwen)
  context: [topicAnalysisTask, contentStrategyTask, primaryResearchTask, gapAnalysisTask, deepResearchTask, contentGenerationTask],
});

logKaibanTask('Content generation and quality assurance tasks created');

/**
 * Export all tasks in execution order
 */
export const superAgentTasks = [
  topicAnalysisTask,
  contentStrategyTask,
  primaryResearchTask,
  gapAnalysisTask,
  deepResearchTask,
  contentGenerationTask,
  qualityAssuranceTask,
];

/**
 * Task configuration for easy reference
 */
export const taskConfig = {
  tasks: superAgentTasks,
  taskNames: [
    'Topic Analysis',
    'Content Strategy',
    'Primary Research',
    'Gap Analysis',
    'Deep Research',
    'Content Generation',
    'Quality Assurance'
  ],
  totalTasks: superAgentTasks.length,
  phaseDistribution: {
    llama: ['Topic Analysis', 'Gap Analysis', 'Quality Assurance'],
    gemini: ['Content Strategy', 'Primary Research', 'Deep Research', 'Content Generation']
  },
  tasksWithTools: ['Primary Research', 'Deep Research']
};

logKaibanTask('KaibanJS Task System initialization complete', {
  status: 'ready',
  totalTasks: 7,
  sequentialExecution: true,
  taskDependencies: {
    'Content Strategy': ['Topic Analysis'],
    'Primary Research': ['Topic Analysis', 'Content Strategy'],
    'Gap Analysis': ['Topic Analysis', 'Content Strategy', 'Primary Research'],
    'Deep Research': ['Topic Analysis', 'Content Strategy', 'Primary Research', 'Gap Analysis'],
    'Content Generation': ['All Previous Tasks'],
    'Quality Assurance': ['All Tasks']
  },
  timestamp: new Date().toISOString()
});
