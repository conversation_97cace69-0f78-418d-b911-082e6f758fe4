/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/kaiban-super-agent/route";
exports.ids = ["app/api/kaiban-super-agent/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-super-agent%2Froute&page=%2Fapi%2Fkaiban-super-agent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-super-agent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-super-agent%2Froute&page=%2Fapi%2Fkaiban-super-agent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-super-agent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_kaiban_super_agent_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/kaiban-super-agent/route.ts */ \"(rsc)/./src/app/api/kaiban-super-agent/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/kaiban-super-agent/route\",\n        pathname: \"/api/kaiban-super-agent\",\n        filename: \"route\",\n        bundlePath: \"app/api/kaiban-super-agent/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/kaiban-super-agent/route.ts\",\n    nextConfigOutput,\n    userland: _Users_aayushmishra_Desktop_old_invincible_with_deepresearch_src_app_api_kaiban_super_agent_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-super-agent%2Froute&page=%2Fapi%2Fkaiban-super-agent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-super-agent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/kaiban-super-agent/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/kaiban-super-agent/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   OPTIONS: () => (/* binding */ OPTIONS),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_agents_kaiban_super_agent__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/agents/kaiban-super-agent */ \"(rsc)/./src/lib/agents/kaiban-super-agent/index.ts\");\n/**\n * KaibanJS Super Agent API Endpoint\n * \n * This API endpoint handles requests for the KaibanJS super agent workflow.\n * It processes content generation requests using the 7-phase workflow with\n * Gemini and Qwen models, integrated with Tavily search.\n */ \n\n// API logging utility\nconst logAPI = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🌐 [KAIBAN-API] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-API-DATA]:`, JSON.stringify(data, null, 2));\n    }\n};\n/**\n * POST handler for KaibanJS Super Agent workflow\n */ async function POST(request) {\n    const startTime = Date.now();\n    try {\n        logAPI('Received KaibanJS Super Agent request');\n        // Parse request body\n        const body = await request.json();\n        // Validate required fields\n        if (!body.topic || typeof body.topic !== 'string' || body.topic.trim().length === 0) {\n            logAPI('Invalid request: missing or empty topic');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: 'Topic is required and must be a non-empty string',\n                timestamp: new Date().toISOString()\n            }, {\n                status: 400\n            });\n        }\n        // Validate environment variables\n        if (false) {}\n        if (false) {}\n        if (false) {}\n        // Prepare options\n        const options = {\n            topic: body.topic.trim(),\n            contentType: body.contentType || 'article',\n            targetWordCount: body.targetWordCount || 2000,\n            tone: body.tone || 'professional',\n            targetAudience: body.targetAudience || 'intermediate',\n            maxPrimaryResults: body.maxPrimaryResults || 6,\n            maxDeepResults: body.maxDeepResults || 4,\n            enableFactChecking: body.enableFactChecking ?? true,\n            includeSourceCitations: body.includeSourceCitations ?? true\n        };\n        logAPI('Starting KaibanJS Super Agent workflow', {\n            topic: options.topic,\n            contentType: options.contentType,\n            targetWordCount: options.targetWordCount,\n            targetAudience: options.targetAudience\n        });\n        // Create progress callback for logging\n        const onProgress = (phase, progress, message)=>{\n            logAPI(`Progress: ${phase} (${progress}%)`, {\n                message\n            });\n        };\n        // Initialize and execute the super agent\n        const superAgent = new _lib_agents_kaiban_super_agent__WEBPACK_IMPORTED_MODULE_1__.KaibanSuperAgent(onProgress);\n        const result = await superAgent.execute(options);\n        const totalTime = Date.now() - startTime;\n        if (result.success) {\n            logAPI('KaibanJS Super Agent workflow completed successfully', {\n                executionTime: `${totalTime}ms`,\n                wordCount: result.wordCount,\n                sourcesUsed: result.sourcesUsed,\n                qualityScore: result.qualityScore\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: result,\n                timestamp: new Date().toISOString()\n            });\n        } else {\n            logAPI('KaibanJS Super Agent workflow failed', {\n                error: result.error,\n                executionTime: `${totalTime}ms`\n            });\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                error: result.error || 'Workflow execution failed',\n                timestamp: new Date().toISOString()\n            }, {\n                status: 500\n            });\n        }\n    } catch (error) {\n        const totalTime = Date.now() - startTime;\n        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';\n        logAPI('KaibanJS Super Agent API error', {\n            error: errorMessage,\n            executionTime: `${totalTime}ms`,\n            stack: error instanceof Error ? error.stack : undefined\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: errorMessage,\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * GET handler for API status and configuration\n */ async function GET() {\n    try {\n        logAPI('Health check request received');\n        const status = {\n            service: 'KaibanJS Super Agent API',\n            status: 'operational',\n            version: '1.0.0',\n            features: [\n                '7-phase super agent workflow',\n                'Gemini and Qwen model integration',\n                'Tavily search integration',\n                'Comprehensive content generation',\n                'Quality assurance and fact-checking'\n            ],\n            configuration: {\n                geminiConfigured: !!\"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\",\n                openrouterConfigured: !!\"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\",\n                tavilyConfigured: !!\"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\",\n                totalAgents: 7,\n                totalTasks: 7,\n                supportedContentTypes: [\n                    'article',\n                    'blog-post',\n                    'research-paper',\n                    'comprehensive-guide',\n                    'case-study',\n                    'white-paper',\n                    'tutorial'\n                ],\n                supportedTones: [\n                    'professional',\n                    'casual',\n                    'academic',\n                    'conversational',\n                    'authoritative',\n                    'engaging',\n                    'technical'\n                ],\n                supportedAudiences: [\n                    'beginner',\n                    'intermediate',\n                    'expert',\n                    'general',\n                    'technical',\n                    'business'\n                ]\n            },\n            timestamp: new Date().toISOString()\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(status);\n    } catch (error) {\n        logAPI('Health check error', {\n            error\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            service: 'KaibanJS Super Agent API',\n            status: 'error',\n            error: error instanceof Error ? error.message : 'Unknown error',\n            timestamp: new Date().toISOString()\n        }, {\n            status: 500\n        });\n    }\n}\n/**\n * OPTIONS handler for CORS\n */ async function OPTIONS() {\n    return new next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse(null, {\n        status: 200,\n        headers: {\n            'Access-Control-Allow-Origin': '*',\n            'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',\n            'Access-Control-Allow-Headers': 'Content-Type, Authorization'\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/kaiban-super-agent/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban-super-agent/agents.ts":
/*!*****************************************************!*\
  !*** ./src/lib/agents/kaiban-super-agent/agents.ts ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   agentConfig: () => (/* binding */ agentConfig),\n/* harmony export */   contentGenerationAgent: () => (/* binding */ contentGenerationAgent),\n/* harmony export */   contentStrategyAgent: () => (/* binding */ contentStrategyAgent),\n/* harmony export */   deepResearchAgent: () => (/* binding */ deepResearchAgent),\n/* harmony export */   gapAnalysisAgent: () => (/* binding */ gapAnalysisAgent),\n/* harmony export */   primaryResearchAgent: () => (/* binding */ primaryResearchAgent),\n/* harmony export */   qualityAssuranceAgent: () => (/* binding */ qualityAssuranceAgent),\n/* harmony export */   superAgentTeam: () => (/* binding */ superAgentTeam),\n/* harmony export */   topicAnalysisAgent: () => (/* binding */ topicAnalysisAgent)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tools */ \"(rsc)/./src/lib/agents/kaiban-super-agent/tools.ts\");\n/**\n * KaibanJS Agents Configuration for Super Agent Workflow\n * \n * This file defines all the agents that will execute the super agent workflow\n * using KaibanJS framework with Gemini and Qwen models.\n * \n * Following user preferences:\n * - Use Qwen model for high reasoning phases (1, 4, 7)\n * - Use Gemini model for other phases\n * - Double the max output and input tokens\n * - Use Tavily search for web research\n */ \n\n// Console logging utility for Kaiban Agents\nconst logKaibanAgent = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🤖 [KAIBAN-AGENT] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-AGENT-DATA]:`, data);\n    }\n};\n/**\n * Model Configuration\n * Following user preferences for doubled tokens and specific model usage\n */ const QWEN_MODEL_CONFIG = {\n    provider: 'openai',\n    model: 'meta-llama/llama-3.1-8b-instruct:free',\n    apiKey: \"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\",\n    baseURL: 'https://openrouter.ai/api/v1',\n    maxTokens: 4000,\n    temperature: 0.3,\n    defaultHeaders: {\n        'HTTP-Referer': 'https://localhost:3000',\n        'X-Title': 'KaibanJS Super Agent'\n    }\n};\nconst GEMINI_MODEL_CONFIG = {\n    provider: 'google',\n    model: 'gemini-2.0-flash',\n    apiKey: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\",\n    maxTokens: 8000,\n    temperature: 0.7\n};\nlogKaibanAgent('Initializing KaibanJS Super Agent Team');\n/**\n * Phase 1: Topic Analysis Agent (High Reasoning - Qwen)\n * Analyzes the input topic and creates comprehensive research strategy\n */ const topicAnalysisAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'TopicAnalyst',\n    role: 'Senior Research Strategist',\n    goal: 'Analyze topics comprehensively and create detailed research strategies for content creation',\n    background: 'Expert research strategist with deep knowledge across multiple domains, specializing in topic analysis and research planning.',\n    llmConfig: QWEN_MODEL_CONFIG,\n    tools: []\n});\n/**\n * Phase 2: Content Strategy Agent (Gemini)\n * Develops content strategy based on topic analysis\n */ const contentStrategyAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'ContentStrategist',\n    role: 'Professional Content Strategist',\n    goal: 'Develop comprehensive content strategies that align with audience needs and business objectives',\n    background: 'Seasoned content strategist with expertise in audience analysis, content planning, and engagement optimization.',\n    llmConfig: GEMINI_MODEL_CONFIG,\n    tools: []\n});\n/**\n * Phase 3: Primary Research Agent (Gemini + Tavily)\n * Conducts initial web research using Tavily search\n */ const primaryResearchAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'PrimaryResearcher',\n    role: 'Senior Research Analyst',\n    goal: 'Conduct comprehensive primary research using advanced search techniques and reliable sources',\n    background: 'Expert research analyst specializing in information gathering and source evaluation using advanced search strategies.',\n    llmConfig: GEMINI_MODEL_CONFIG,\n    tools: _tools__WEBPACK_IMPORTED_MODULE_1__.kaibanTools\n});\n/**\n * Phase 4: Gap Analysis Agent (High Reasoning - Qwen)\n * Analyzes research gaps and determines additional research needs\n */ const gapAnalysisAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'GapAnalyst',\n    role: 'Research Quality Analyst',\n    goal: 'Identify content gaps and determine additional research requirements for comprehensive coverage',\n    background: 'Meticulous research quality analyst with expertise in identifying information gaps and ensuring comprehensive topic coverage.',\n    llmConfig: QWEN_MODEL_CONFIG,\n    tools: []\n});\n/**\n * Phase 5: Deep Research Agent (Gemini + Tavily)\n * Conducts targeted deep research based on gap analysis\n */ const deepResearchAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'DeepResearcher',\n    role: 'Specialized Research Expert',\n    goal: 'Conduct targeted deep research to fill identified gaps and enhance content comprehensiveness',\n    background: 'Specialized research expert who excels at finding specific, detailed information to fill knowledge gaps.',\n    llmConfig: GEMINI_MODEL_CONFIG,\n    tools: _tools__WEBPACK_IMPORTED_MODULE_1__.kaibanTools\n});\n/**\n * Phase 6: Content Generation Agent (Gemini)\n * Generates high-quality content based on research\n */ const contentGenerationAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'ContentWriter',\n    role: 'Expert Content Creator',\n    goal: 'Create engaging, well-structured, and authoritative content based on comprehensive research',\n    background: 'Expert content creator with exceptional writing skills and deep understanding of audience engagement.',\n    llmConfig: GEMINI_MODEL_CONFIG,\n    tools: []\n});\n/**\n * Phase 7: Quality Assurance Agent (High Reasoning - Qwen)\n * Reviews and optimizes the final content\n */ const qualityAssuranceAgent = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Agent({\n    name: 'QualityAssurance',\n    role: 'Senior Content Quality Analyst',\n    goal: 'Ensure content meets highest quality standards through comprehensive review and optimization',\n    background: 'Senior content quality analyst with expertise in fact-checking, readability optimization, and content enhancement.',\n    llmConfig: QWEN_MODEL_CONFIG,\n    tools: []\n});\n/**\n * Export all agents as a team\n */ const superAgentTeam = [\n    topicAnalysisAgent,\n    contentStrategyAgent,\n    primaryResearchAgent,\n    gapAnalysisAgent,\n    deepResearchAgent,\n    contentGenerationAgent,\n    qualityAssuranceAgent\n];\nlogKaibanAgent('Super Agent Team created successfully', {\n    totalAgents: superAgentTeam.length,\n    llamaAgents: [\n        'TopicAnalyst',\n        'GapAnalyst',\n        'QualityAssurance'\n    ],\n    geminiAgents: [\n        'ContentStrategist',\n        'PrimaryResearcher',\n        'DeepResearcher',\n        'ContentWriter'\n    ],\n    agentsWithTools: [\n        'PrimaryResearcher',\n        'DeepResearcher'\n    ],\n    maxTokens: 4000,\n    status: 'ready'\n});\n/**\n * Agent configuration for easy reference\n */ const agentConfig = {\n    agents: superAgentTeam,\n    agentNames: [\n        'TopicAnalyst',\n        'ContentStrategist',\n        'PrimaryResearcher',\n        'GapAnalyst',\n        'DeepResearcher',\n        'ContentWriter',\n        'QualityAssurance'\n    ],\n    totalAgents: superAgentTeam.length,\n    modelDistribution: {\n        llama: 3,\n        gemini: 4 // Other phases\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban-super-agent/agents.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban-super-agent/index.ts":
/*!****************************************************!*\
  !*** ./src/lib/agents/kaiban-super-agent/index.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KaibanSuperAgent: () => (/* binding */ KaibanSuperAgent),\n/* harmony export */   agentConfig: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig),\n/* harmony export */   kaibanTools: () => (/* reexport safe */ _tools__WEBPACK_IMPORTED_MODULE_3__.kaibanTools),\n/* harmony export */   superAgentTasks: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.superAgentTasks),\n/* harmony export */   superAgentTeam: () => (/* reexport safe */ _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam),\n/* harmony export */   taskConfig: () => (/* reexport safe */ _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/agents/kaiban-super-agent/agents.ts\");\n/* harmony import */ var _tasks__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./tasks */ \"(rsc)/./src/lib/agents/kaiban-super-agent/tasks.ts\");\n/* harmony import */ var _tools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tools */ \"(rsc)/./src/lib/agents/kaiban-super-agent/tools.ts\");\n/**\n * KaibanJS Super Agent System\n * \n * Main orchestration system for the KaibanJS-based super agent workflow.\n * Integrates Gemini and Qwen models with Tavily search following the\n * super agent workflow pattern.\n * \n * Features:\n * - 7-phase super agent workflow\n * - Qwen model for high reasoning phases (1, 4, 7)\n * - Gemini model for other phases\n * - Tavily search integration\n * - Comprehensive error handling and logging\n */ \n\n\n\n// Console logging utility for Kaiban System\nconst logKaibanSystem = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🚀 [KAIBAN-SYSTEM] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-SYSTEM-DATA]:`, data);\n    }\n};\n/**\n * KaibanJS Super Agent Class\n */ class KaibanSuperAgent {\n    constructor(onProgress){\n        this.isInitialized = false;\n        this.onProgress = onProgress;\n        logKaibanSystem('Initializing KaibanJS Super Agent System');\n        // Validate tools before initialization\n        if (!(0,_tools__WEBPACK_IMPORTED_MODULE_3__.validateTavilyTool)()) {\n            throw new Error('Tavily tool validation failed. Please check your API key configuration.');\n        }\n        // Create the KaibanJS team\n        this.team = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Team({\n            name: 'Super Agent Team',\n            agents: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam,\n            tasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.superAgentTasks,\n            inputs: {\n                topic: '',\n                contentType: 'article',\n                targetAudience: 'intermediate',\n                wordCount: 2000,\n                tone: 'professional'\n            },\n            env: {\n                OPENROUTER_API_KEY: \"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\",\n                GEMINI_API_KEY: \"AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY\",\n                TAVILY_API_KEY: \"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\"\n            }\n        });\n        this.isInitialized = true;\n        logKaibanSystem('KaibanJS Super Agent System initialized successfully', {\n            totalAgents: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.totalAgents,\n            totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n            toolsConfigured: [\n                'KaibanJS TavilySearchResults'\n            ],\n            modelsConfigured: [\n                'meta-llama/llama-3.1-8b-instruct:free',\n                'gemini-2.0-flash'\n            ]\n        });\n    }\n    /**\n   * Execute the super agent workflow\n   */ async execute(options) {\n        if (!this.isInitialized) {\n            throw new Error('KaibanJS Super Agent System not initialized');\n        }\n        const startTime = Date.now();\n        logKaibanSystem('Starting super agent workflow execution', {\n            topic: options.topic,\n            contentType: options.contentType,\n            targetWordCount: options.targetWordCount,\n            targetAudience: options.targetAudience\n        });\n        try {\n            // Update progress\n            this.updateProgress('initialization', 0, 'Initializing workflow...');\n            // Execute the workflow - start() method returns the WorkflowResult directly\n            this.updateProgress('execution', 10, 'Starting task execution...');\n            const result = await this.team.start({\n                topic: options.topic,\n                contentType: options.contentType || 'article',\n                targetAudience: options.targetAudience || 'intermediate',\n                wordCount: options.targetWordCount || 2000,\n                tone: options.tone || 'professional'\n            });\n            const executionTime = Date.now() - startTime;\n            logKaibanSystem('Super agent workflow completed successfully', {\n                executionTime: `${executionTime}ms`,\n                tasksCompleted: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                workflowResult: result\n            });\n            // Process and return results\n            return this.processResults(result, executionTime);\n        } catch (error) {\n            const executionTime = Date.now() - startTime;\n            logKaibanSystem('Super agent workflow failed', {\n                error: error instanceof Error ? error.message : 'Unknown error',\n                executionTime: `${executionTime}ms`\n            });\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error occurred',\n                executionTime,\n                metadata: {\n                    totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    completedTasks: 0,\n                    failedTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    agentsUsed: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.agentNames,\n                    modelsUsed: [\n                        'meta-llama/llama-3.1-8b-instruct:free',\n                        'gemini-2.0-flash'\n                    ],\n                    searchQueriesExecuted: 0\n                }\n            };\n        }\n    }\n    /**\n   * Process workflow results\n   */ processResults(result, executionTime) {\n        try {\n            // KaibanJS WorkflowResult structure: { result: string, stats: WorkflowStats }\n            const finalOutput = result.result || result.output || 'No content generated';\n            const stats = result.stats || {};\n            logKaibanSystem('Processing workflow results', {\n                hasResult: !!result.result,\n                hasStats: !!result.stats,\n                resultType: typeof result.result,\n                statsKeys: Object.keys(stats)\n            });\n            return {\n                success: true,\n                content: finalOutput,\n                title: this.extractTitle(finalOutput),\n                wordCount: this.countWords(finalOutput),\n                sourcesUsed: this.countSources(result),\n                qualityScore: this.calculateQualityScore(result),\n                executionTime,\n                phaseResults: this.extractPhaseResults(result),\n                metadata: {\n                    totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    completedTasks: stats.completedTasks || _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    failedTasks: stats.failedTasks || 0,\n                    agentsUsed: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.agentNames,\n                    modelsUsed: [\n                        'meta-llama/llama-3.1-8b-instruct:free',\n                        'gemini-2.0-flash'\n                    ],\n                    searchQueriesExecuted: this.countSearchQueries(result)\n                }\n            };\n        } catch (error) {\n            logKaibanSystem('Error processing results', {\n                error\n            });\n            return {\n                success: false,\n                error: 'Failed to process workflow results',\n                executionTime,\n                metadata: {\n                    totalTasks: _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.totalTasks,\n                    completedTasks: 0,\n                    failedTasks: 1,\n                    agentsUsed: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.agentNames,\n                    modelsUsed: [\n                        'meta-llama/llama-3.1-8b-instruct:free',\n                        'gemini-2.0-flash'\n                    ],\n                    searchQueriesExecuted: 0\n                }\n            };\n        }\n    }\n    /**\n   * Update progress callback\n   */ updateProgress(phase, progress, message) {\n        if (this.onProgress) {\n            this.onProgress(phase, progress, message);\n        }\n    }\n    /**\n   * Helper methods for result processing\n   */ extractTitle(content) {\n        const lines = content.split('\\n');\n        const titleLine = lines.find((line)=>line.trim().startsWith('#') || line.trim().length > 10);\n        return titleLine?.replace(/^#+\\s*/, '').trim() || 'Generated Content';\n    }\n    countWords(content) {\n        return content.split(/\\s+/).filter((word)=>word.length > 0).length;\n    }\n    countSources(result) {\n        // Count unique sources from the result content\n        const content = result.result || result.output || '';\n        if (typeof content === 'string') {\n            const urls = content.match(/https?:\\/\\/[^\\s]+/g) || [];\n            return new Set(urls).size;\n        }\n        return 0;\n    }\n    calculateQualityScore(result) {\n        // Simple quality scoring based on result availability and stats\n        const stats = result.stats || {};\n        if (stats.completedTasks && stats.totalTasks) {\n            return Math.round(stats.completedTasks / stats.totalTasks * 100);\n        }\n        // If we have a result, assume good quality\n        return result.result ? 85 : 0;\n    }\n    extractPhaseResults(result) {\n        const phaseResults = {};\n        const stats = result.stats || {};\n        // Create phase results based on available information\n        _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.taskNames.forEach((phaseName, index)=>{\n            phaseResults[phaseName] = {\n                status: 'completed',\n                output: index === _tasks__WEBPACK_IMPORTED_MODULE_2__.taskConfig.taskNames.length - 1 ? result.result : 'Phase completed',\n                agent: _agents__WEBPACK_IMPORTED_MODULE_1__.agentConfig.agentNames[index],\n                executionTime: 0 // KaibanJS doesn't provide individual task times\n            };\n        });\n        return phaseResults;\n    }\n    countSearchQueries(result) {\n        // Estimate search queries based on content\n        const content = result.result || result.output || '';\n        if (typeof content === 'string') {\n            // Look for search-related indicators\n            const searchMatches = content.match(/search|research|found|according to|source/gi) || [];\n            return Math.min(searchMatches.length, 20); // Cap at reasonable number\n        }\n        return 0;\n    }\n}\n/**\n * Export configuration and utilities\n */ \n\n\n\nlogKaibanSystem('KaibanJS Super Agent System module loaded successfully');\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban-super-agent/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban-super-agent/tasks.ts":
/*!****************************************************!*\
  !*** ./src/lib/agents/kaiban-super-agent/tasks.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   contentGenerationTask: () => (/* binding */ contentGenerationTask),\n/* harmony export */   contentStrategyTask: () => (/* binding */ contentStrategyTask),\n/* harmony export */   deepResearchTask: () => (/* binding */ deepResearchTask),\n/* harmony export */   gapAnalysisTask: () => (/* binding */ gapAnalysisTask),\n/* harmony export */   primaryResearchTask: () => (/* binding */ primaryResearchTask),\n/* harmony export */   qualityAssuranceTask: () => (/* binding */ qualityAssuranceTask),\n/* harmony export */   superAgentTasks: () => (/* binding */ superAgentTasks),\n/* harmony export */   taskConfig: () => (/* binding */ taskConfig),\n/* harmony export */   topicAnalysisTask: () => (/* binding */ topicAnalysisTask)\n/* harmony export */ });\n/* harmony import */ var kaibanjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! kaibanjs */ \"(rsc)/./node_modules/kaibanjs/dist/bundle.mjs\");\n/* harmony import */ var _agents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./agents */ \"(rsc)/./src/lib/agents/kaiban-super-agent/agents.ts\");\n/**\n * KaibanJS Tasks for Super Agent Workflow\n *\n * This file defines all the tasks that agents will execute in sequence\n * to complete the super agent workflow using KaibanJS framework.\n * \n * Following the 7-phase super agent workflow pattern with enhanced\n * task definitions and proper agent assignments.\n */ \n\n// Console logging utility for Kaiban Tasks\nconst logKaibanTask = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`📋 [KAIBAN-TASK] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-TASK-DATA]:`, data);\n    }\n};\nlogKaibanTask('Initializing KaibanJS Super Agent Tasks');\n/**\n * Phase 1: Topic Analysis Task (Llama - High Reasoning)\n * Comprehensive topic analysis and research strategy development\n */ const topicAnalysisTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Analyze the given topic comprehensively and create a detailed research strategy.\n    \n    **Your Role:** You are a Senior Research Strategist with expertise in topic analysis and research planning.\n    \n    **Task Requirements:**\n    1. **Topic Breakdown**: Analyze the main topic and identify 4-6 key subtopics\n    2. **Key Terms Extraction**: Extract 6-8 important terms including technical terminology\n    3. **Search Strategy**: Create 3-4 strategic search queries for comprehensive coverage\n    4. **Complexity Assessment**: Evaluate topic complexity (simple/moderate/complex)\n    5. **Source Estimation**: Estimate realistic source count needed (8-15 sources)\n    6. **Audience Analysis**: Determine target audience intent and expertise level\n    7. **Content Angle**: Identify unique perspective or approach for the topic\n    8. **Competitive Keywords**: Find terms competitors are ranking for\n    9. **Trending Topics**: Identify related topics that are currently popular\n    10. **Content Gaps**: Spot missing information in existing content\n    11. **User Questions**: Generate questions the audience would ask\n    12. **Semantic Keywords**: Extract related terms and concepts\n    \n    **Output Format:**\n    Provide a structured analysis with clear sections for each requirement.\n    Focus on creating a comprehensive foundation for the research phases.\n    \n    **Topic to Analyze:** {topic}\n    **Content Type:** {contentType}\n    **Target Audience:** {targetAudience}\n    **Word Count Target:** {wordCount}`,\n    expectedOutput: `Comprehensive topic analysis including:\n    - Main topic and subtopics breakdown\n    - Key terms and semantic keywords\n    - Strategic search queries\n    - Complexity assessment and source estimation\n    - Audience intent and content angle analysis\n    - Competitive keywords and trending topics\n    - Content gaps and user questions\n    - Research strategy recommendations`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[0]\n});\n/**\n * Phase 2: Content Strategy Task (Gemini)\n * Develop comprehensive content strategy based on topic analysis\n */ const contentStrategyTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Develop a comprehensive content strategy based on the topic analysis.\n    \n    **Your Role:** You are a Professional Content Strategist specializing in audience-focused content planning.\n    \n    **Task Requirements:**\n    1. **Content Goal Definition**: Define clear content objectives\n    2. **Audience Targeting**: Refine target audience based on analysis\n    3. **Content Structure**: Design optimal content structure and flow\n    4. **Key Messages**: Identify 3-5 core messages to communicate\n    5. **Hook Development**: Create compelling opening hooks\n    6. **Call-to-Action Strategy**: Design effective CTAs\n    7. **SEO Strategy**: Develop keyword strategy and search intent alignment\n    8. **Narrative Structure**: Plan story arc and content flow\n    9. **Engagement Elements**: Identify opportunities for audience engagement\n    10. **Content Differentiation**: Define unique value proposition\n    \n    **Input:** Use the topic analysis results from the previous task.\n    \n    **Output Format:**\n    Provide a detailed content strategy document with actionable recommendations.`,\n    expectedOutput: `Detailed content strategy including:\n    - Content goals and audience targeting\n    - Recommended content structure and flow\n    - Key messages and narrative structure\n    - Hook strategies and call-to-action plans\n    - SEO strategy with keyword recommendations\n    - Engagement and differentiation strategies`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[1],\n    context: [\n        topicAnalysisTask\n    ]\n});\n/**\n * Phase 3: Primary Research Task (Gemini + Tavily)\n * Conduct comprehensive primary research using Tavily search\n */ const primaryResearchTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Conduct comprehensive primary research using the Tavily search tool.\n    \n    **Your Role:** You are a Senior Research Analyst specializing in information gathering and source evaluation.\n    \n    **Task Requirements:**\n    1. **Execute Search Queries**: Use the strategic queries from topic analysis\n    2. **Source Evaluation**: Assess source credibility and authority\n    3. **Information Extraction**: Extract key information and insights\n    4. **Data Organization**: Structure findings by subtopic and relevance\n    5. **Source Documentation**: Maintain detailed source citations\n    6. **Quality Filtering**: Filter out low-quality or unreliable sources\n    7. **Trend Identification**: Identify current trends and developments\n    8. **Expert Insights**: Find expert opinions and authoritative perspectives\n    9. **Statistical Data**: Gather relevant statistics and data points\n    10. **Recent Developments**: Focus on current and recent information (2024-2025)\n    \n    **Search Strategy:**\n    - Use exact topic searches first\n    - Execute targeted queries for specific aspects\n    - Search for authoritative and expert sources\n    - Focus on recent content and current trends\n    - Maximum 10 results per search query\n    \n    **Tool Usage:**\n    Use the Tavily search tool by calling it with your search queries.\n    Example: Search for \"artificial intelligence trends 2025\"\n    \n    **Input:** Use search queries and strategy from topic analysis and content strategy tasks.`,\n    expectedOutput: `Comprehensive research findings including:\n    - Organized information by subtopic\n    - Source citations and credibility assessments\n    - Key insights and expert perspectives\n    - Current trends and recent developments\n    - Statistical data and supporting evidence\n    - Identified information gaps for deep research`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[2],\n    context: [\n        topicAnalysisTask,\n        contentStrategyTask\n    ]\n});\nlogKaibanTask('Primary research task created with Tavily integration');\n/**\n * Phase 4: Gap Analysis Task (Llama - High Reasoning)\n * Analyze research gaps and determine additional research needs\n */ const gapAnalysisTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Analyze the primary research results to identify gaps and determine additional research needs.\n    \n    **Your Role:** You are a Research Quality Analyst with expertise in identifying information gaps and ensuring comprehensive coverage.\n    \n    **Task Requirements:**\n    1. **Coverage Assessment**: Evaluate completeness of primary research\n    2. **Gap Identification**: Identify missing information and perspectives\n    3. **Priority Analysis**: Rank gaps by importance and impact\n    4. **Research Recommendations**: Suggest specific additional research queries\n    5. **Source Diversity**: Assess need for different types of sources\n    6. **Depth Analysis**: Determine areas needing deeper investigation\n    7. **Quality Evaluation**: Assess overall research quality and reliability\n    8. **Bias Detection**: Identify potential biases in current research\n    9. **Expertise Gaps**: Find areas lacking expert perspectives\n    10. **Currency Assessment**: Evaluate need for more recent information\n    \n    **Analysis Framework:**\n    - Compare research against content strategy requirements\n    - Identify subtopics with insufficient coverage\n    - Assess source diversity and authority\n    - Evaluate information recency and relevance\n    - Determine critical missing elements\n    \n    **Input:** Use results from primary research and compare against topic analysis and content strategy.`,\n    expectedOutput: `Comprehensive gap analysis including:\n    - Identified information gaps with priority rankings\n    - Specific additional research recommendations\n    - Source diversity and quality assessment\n    - Areas requiring deeper investigation\n    - Targeted search queries for deep research phase`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[3],\n    context: [\n        topicAnalysisTask,\n        contentStrategyTask,\n        primaryResearchTask\n    ]\n});\nlogKaibanTask('Gap analysis task created for research quality assessment');\n/**\n * Phase 5: Deep Research Task (Gemini + Tavily)\n * Conduct targeted deep research based on gap analysis\n */ const deepResearchTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Conduct targeted deep research to fill identified gaps and enhance content comprehensiveness.\n    \n    **Your Role:** You are a Specialized Research Expert who excels at finding specific, detailed information to fill knowledge gaps.\n    \n    **Task Requirements:**\n    1. **Targeted Searches**: Execute specific queries to fill identified gaps\n    2. **Expert Source Finding**: Locate authoritative expert perspectives\n    3. **Detailed Investigation**: Dive deep into complex aspects\n    4. **Specialized Information**: Find technical or specialized details\n    5. **Case Studies**: Locate relevant examples and case studies\n    6. **Recent Developments**: Find latest news and developments\n    7. **Statistical Enhancement**: Gather additional data and statistics\n    8. **Comparative Analysis**: Find comparative information and benchmarks\n    9. **Implementation Details**: Research practical applications and methods\n    10. **Validation**: Cross-reference and validate critical information\n    \n    **Search Strategy:**\n    - Focus on gap analysis recommendations\n    - Use specialized and technical search terms\n    - Target expert and authoritative sources\n    - Search for recent developments and updates\n    - Look for case studies and practical examples\n    \n    **Tool Usage:**\n    Use the Tavily search tool with targeted queries based on gap analysis.\n    \n    **Input:** Use gap analysis recommendations and specific research needs identified.`,\n    expectedOutput: `Targeted deep research results including:\n    - Specific information filling identified gaps\n    - Expert perspectives and authoritative sources\n    - Detailed technical or specialized information\n    - Case studies and practical examples\n    - Recent developments and updates\n    - Enhanced statistical data and evidence`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[4],\n    context: [\n        topicAnalysisTask,\n        contentStrategyTask,\n        primaryResearchTask,\n        gapAnalysisTask\n    ]\n});\nlogKaibanTask('Deep research task created for targeted investigation');\n/**\n * Phase 6: Content Generation Task (Gemini)\n * Generate high-quality content based on comprehensive research\n */ const contentGenerationTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Create engaging, well-structured, and authoritative content based on comprehensive research.\n\n    **Your Role:** You are an Expert Content Creator with exceptional writing skills and deep understanding of audience engagement.\n\n    **Task Requirements:**\n    1. **Content Structure**: Follow the content strategy structure and flow\n    2. **Research Integration**: Seamlessly integrate research findings\n    3. **Engaging Writing**: Create compelling and readable content\n    4. **Authority Building**: Establish credibility through expert sources\n    5. **Audience Focus**: Write for the specified target audience\n    6. **Hook Implementation**: Use effective opening hooks from strategy\n    7. **Flow Optimization**: Ensure smooth transitions between sections\n    8. **Evidence Integration**: Include supporting data and statistics\n    9. **Source Attribution**: Properly cite sources and references\n    10. **Call-to-Action**: Include strategic CTAs as planned\n    11. **SEO Integration**: Naturally incorporate target keywords\n    12. **Readability**: Maintain appropriate reading level and clarity\n\n    **Content Guidelines:**\n    - Follow the target word count: {wordCount}\n    - Use the specified tone: {tone}\n    - Address the target audience: {targetAudience}\n    - Include 2025 trends and current year information\n    - Reference authoritative sources from research\n    - Create paragraph-sized data points for better context\n    - Strictly adhere to the exact topic without deviation\n\n    **Input:** Use all previous task results including research findings, content strategy, and gap analysis.`,\n    expectedOutput: `High-quality content including:\n    - Complete article following content strategy\n    - Proper integration of research findings\n    - Engaging hooks and smooth flow\n    - Authoritative source citations\n    - Strategic call-to-actions\n    - SEO-optimized content with target keywords\n    - Content meeting specified word count and tone`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[5],\n    context: [\n        topicAnalysisTask,\n        contentStrategyTask,\n        primaryResearchTask,\n        gapAnalysisTask,\n        deepResearchTask\n    ]\n});\n/**\n * Phase 7: Quality Assurance Task (Llama - High Reasoning)\n * Review and optimize the final content\n */ const qualityAssuranceTask = new kaibanjs__WEBPACK_IMPORTED_MODULE_0__.Task({\n    description: `Ensure content meets highest quality standards through comprehensive review and optimization.\n\n    **Your Role:** You are a Senior Content Quality Analyst with expertise in fact-checking, readability optimization, and content enhancement.\n\n    **Task Requirements:**\n    1. **Fact Verification**: Verify all claims and statistics\n    2. **Source Validation**: Ensure all sources are properly cited and credible\n    3. **Readability Analysis**: Assess and optimize reading level\n    4. **Structure Review**: Evaluate content organization and flow\n    5. **Engagement Assessment**: Review hooks, transitions, and CTAs\n    6. **SEO Optimization**: Verify keyword integration and optimization\n    7. **Accuracy Check**: Ensure technical accuracy and currency\n    8. **Bias Detection**: Identify and address potential biases\n    9. **Completeness Review**: Ensure all key points are covered\n    10. **Final Polish**: Enhance clarity, coherence, and impact\n    11. **Quality Scoring**: Provide comprehensive quality metrics\n    12. **Improvement Recommendations**: Suggest specific enhancements\n\n    **Quality Standards:**\n    - Factual accuracy and source reliability\n    - Appropriate readability for target audience\n    - Strong engagement and storytelling elements\n    - Effective SEO optimization\n    - Comprehensive topic coverage\n    - Professional writing quality\n    - Clear structure and flow\n\n    **Input:** Use the generated content and all supporting research and strategy materials.`,\n    expectedOutput: `Comprehensive quality assessment including:\n    - Fact-checking results and source validation\n    - Readability analysis and optimization recommendations\n    - Content quality scores and metrics\n    - SEO optimization assessment\n    - Engagement and structure evaluation\n    - Final polished content with improvements\n    - Quality assurance report with recommendations`,\n    agent: _agents__WEBPACK_IMPORTED_MODULE_1__.superAgentTeam[6],\n    context: [\n        topicAnalysisTask,\n        contentStrategyTask,\n        primaryResearchTask,\n        gapAnalysisTask,\n        deepResearchTask,\n        contentGenerationTask\n    ]\n});\nlogKaibanTask('Content generation and quality assurance tasks created');\n/**\n * Export all tasks in execution order\n */ const superAgentTasks = [\n    topicAnalysisTask,\n    contentStrategyTask,\n    primaryResearchTask,\n    gapAnalysisTask,\n    deepResearchTask,\n    contentGenerationTask,\n    qualityAssuranceTask\n];\n/**\n * Task configuration for easy reference\n */ const taskConfig = {\n    tasks: superAgentTasks,\n    taskNames: [\n        'Topic Analysis',\n        'Content Strategy',\n        'Primary Research',\n        'Gap Analysis',\n        'Deep Research',\n        'Content Generation',\n        'Quality Assurance'\n    ],\n    totalTasks: superAgentTasks.length,\n    phaseDistribution: {\n        llama: [\n            'Topic Analysis',\n            'Gap Analysis',\n            'Quality Assurance'\n        ],\n        gemini: [\n            'Content Strategy',\n            'Primary Research',\n            'Deep Research',\n            'Content Generation'\n        ]\n    },\n    tasksWithTools: [\n        'Primary Research',\n        'Deep Research'\n    ]\n};\nlogKaibanTask('KaibanJS Task System initialization complete', {\n    status: 'ready',\n    totalTasks: 7,\n    sequentialExecution: true,\n    taskDependencies: {\n        'Content Strategy': [\n            'Topic Analysis'\n        ],\n        'Primary Research': [\n            'Topic Analysis',\n            'Content Strategy'\n        ],\n        'Gap Analysis': [\n            'Topic Analysis',\n            'Content Strategy',\n            'Primary Research'\n        ],\n        'Deep Research': [\n            'Topic Analysis',\n            'Content Strategy',\n            'Primary Research',\n            'Gap Analysis'\n        ],\n        'Content Generation': [\n            'All Previous Tasks'\n        ],\n        'Quality Assurance': [\n            'All Tasks'\n        ]\n    },\n    timestamp: new Date().toISOString()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban-super-agent/tasks.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agents/kaiban-super-agent/tools.ts":
/*!****************************************************!*\
  !*** ./src/lib/agents/kaiban-super-agent/tools.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   kaibanTools: () => (/* binding */ kaibanTools),\n/* harmony export */   tavilySearchTool: () => (/* binding */ tavilySearchTool),\n/* harmony export */   validateTavilyTool: () => (/* binding */ validateTavilyTool)\n/* harmony export */ });\n/* harmony import */ var _kaibanjs_tools__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @kaibanjs/tools */ \"(rsc)/./node_modules/@kaibanjs/tools/dist/index.esm.js\");\n/**\n * KaibanJS Tools Configuration\n *\n * This file configures the Tavily search tool for KaibanJS agents\n * following the official KaibanJS documentation pattern.\n */ \n// Console logging utility for Kaiban Tools\nconst logKaibanTool = (message, data)=>{\n    const timestamp = new Date().toISOString();\n    console.log(`🔧 [KAIBAN-TOOL] ${timestamp}: ${message}`);\n    if (data) {\n        console.log(`📝 [KAIBAN-TOOL-DATA]:`, data);\n    }\n};\n/**\n * Tavily Search Tool Configuration\n *\n * Following the official KaibanJS Tavily search tool implementation pattern:\n * - Import TavilySearchResults from @kaibanjs/tools\n * - Instantiate with apiKey and maxResults parameters\n * - Add to agent's tools array\n */ logKaibanTool('Initializing Tavily Search Tool');\nconst tavilySearchTool = new _kaibanjs_tools__WEBPACK_IMPORTED_MODULE_0__.TavilySearchResults({\n    apiKey: \"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\" || 0,\n    maxResults: 10\n});\nlogKaibanTool('Tavily Search Tool configured', {\n    maxResults: 10,\n    apiKeyConfigured: !!\"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\",\n    toolType: 'KaibanJS TavilySearchResults'\n});\n/**\n * Tool validation function\n */ const validateTavilyTool = ()=>{\n    const isValid = !!\"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\";\n    if (!isValid) {\n        logKaibanTool('❌ Tavily API key not configured', {\n            error: 'TAVILY_API_KEY environment variable is missing',\n            solution: 'Add your Tavily API key to the .env.local file',\n            documentation: 'https://docs.kaibanjs.com/tools-docs/kaibanjs-tools/Tavily'\n        });\n    } else {\n        logKaibanTool('✅ Tavily tool validation passed');\n    }\n    return isValid;\n};\n/**\n * Export tools array for easy import in agents\n */ const kaibanTools = [\n    tavilySearchTool\n];\nlogKaibanTool('KaibanJS Tools initialization complete', {\n    totalTools: kaibanTools.length,\n    toolNames: [\n        'TavilySearchResults'\n    ],\n    toolSource: '@kaibanjs/tools',\n    status: 'ready'\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agents/kaiban-super-agent/tools.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "fs/promises":
/*!******************************!*\
  !*** external "fs/promises" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("fs/promises");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/kaibanjs","vendor-chunks/@kaibanjs"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fkaiban-super-agent%2Froute&page=%2Fapi%2Fkaiban-super-agent%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fkaiban-super-agent%2Froute.ts&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();